#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
稳定启动脚本
带有自动重连和错误恢复功能
"""

import os
import sys
import time
import subprocess
import psutil
from datetime import datetime

# 设置控制台编码
if sys.platform == "win32":
    import locale
    try:
        # 尝试设置UTF-8编码
        os.system('chcp 65001 >nul 2>&1')
    except:
        pass

class StableBot:
    def __init__(self):
        self.max_retries = 5
        self.retry_delay = 10
        self.restart_count = 0
        self.last_restart_time = None
        
    def check_wechat_running(self):
        """检查微信是否运行"""
        for proc in psutil.process_iter(['name']):
            if proc.info['name'] and 'WeChat.exe' in proc.info['name']:
                return True
        return False
    
    def wait_for_wechat(self, timeout=60):
        """等待微信启动"""
        print("等待微信启动...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.check_wechat_running():
                print("✅ 微信已启动")
                time.sleep(5)  # 等待微信完全加载
                return True
            time.sleep(2)
        
        print("❌ 微信启动超时")
        return False
    
    def start_bot(self):
        """启动机器人"""
        try:
            print(f"\n🚀 启动AI客服机器人 (第{self.restart_count + 1}次)")
            print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 检查微信状态
            if not self.check_wechat_running():
                print("⚠️  微信未运行，请先启动微信PC版")
                if not self.wait_for_wechat():
                    return False
            
            # 启动机器人
            process = subprocess.Popen(
                [sys.executable, 'app.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 监控输出
            startup_timeout = 60
            start_time = time.time()
            login_success = False
            
            while True:
                output = process.stdout.readline()
                if output:
                    print(output.strip())
                    
                    # 检查登录成功
                    if "微信登录成功" in output:
                        login_success = True
                        print("✅ 微信登录成功，机器人正常运行")
                        self.last_restart_time = datetime.now()
                    
                    # 检查严重错误
                    if any(error in output for error in [
                        "Connection refused",
                        "Timed out", 
                        "微信闪退",
                        "进程已终止"
                    ]):
                        print("❌ 检测到连接错误")
                        break
                
                # 检查进程状态
                if process.poll() is not None:
                    print("❌ 机器人进程已退出")
                    break
                
                # 启动超时检查
                if not login_success and time.time() - start_time > startup_timeout:
                    print("❌ 启动超时")
                    break
                
                # 如果登录成功，继续监控
                if login_success:
                    time.sleep(1)
            
            # 清理进程
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=10)
            
            return login_success
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
    
    def restart_wechat(self):
        """重启微信"""
        print("🔄 尝试重启微信...")
        
        # 关闭微信进程
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] and 'WeChat.exe' in proc.info['name']:
                try:
                    proc.terminate()
                    proc.wait(timeout=10)
                    print("✅ 微信进程已关闭")
                except:
                    pass
        
        time.sleep(5)
        
        # 尝试启动微信
        wechat_paths = [
            r"C:\Program Files\Tencent\WeChat\WeChat.exe",
            r"C:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
            r"D:\Program Files\Tencent\WeChat\WeChat.exe",
            r"D:\WeChat\WeChat.exe"
        ]
        
        for path in wechat_paths:
            if os.path.exists(path):
                try:
                    subprocess.Popen([path])
                    print(f"✅ 微信启动命令已执行: {path}")
                    return self.wait_for_wechat()
                except:
                    continue
        
        print("❌ 无法自动启动微信，请手动启动")
        return False
    
    def run(self):
        """主运行循环"""
        print("🤖 AI客服机器人稳定启动器")
        print("=" * 50)
        print("功能:")
        print("• 自动检测微信状态")
        print("• 自动重连和错误恢复")
        print("• 智能重启机制")
        print("• 详细日志输出")
        print("=" * 50)
        
        while self.restart_count < self.max_retries:
            try:
                # 启动机器人
                success = self.start_bot()
                
                if success:
                    print("✅ 机器人运行成功")
                    # 如果成功运行，重置重试计数
                    if self.restart_count > 0:
                        print(f"🎉 经过{self.restart_count}次重试后成功启动")
                    break
                else:
                    self.restart_count += 1
                    print(f"❌ 第{self.restart_count}次启动失败")
                    
                    if self.restart_count < self.max_retries:
                        print(f"⏳ {self.retry_delay}秒后重试...")
                        time.sleep(self.retry_delay)
                        
                        # 每2次失败后尝试重启微信
                        if self.restart_count % 2 == 0:
                            self.restart_wechat()
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户手动停止")
                break
            except Exception as e:
                print(f"❌ 运行异常: {e}")
                self.restart_count += 1
                time.sleep(self.retry_delay)
        
        if self.restart_count >= self.max_retries:
            print(f"\n❌ 达到最大重试次数({self.max_retries})，启动失败")
            print("\n🔧 建议:")
            print("1. 检查微信版本兼容性")
            print("2. 运行 python check_wechat_compatibility.py")
            print("3. 手动重启微信后再试")
            print("4. 检查网络连接")

def main():
    """主函数"""
    bot = StableBot()
    bot.run()

if __name__ == "__main__":
    main()
