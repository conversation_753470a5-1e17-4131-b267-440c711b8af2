{"channel_type": "wcf", "model": "Pro/deepseek-ai/DeepSeek-V3", "open_ai_api_base": "https://api.siliconflow.cn/v1", "open_ai_api_key": "sk-ptebmgpiesctnbdmeewgpboobiruklatpuaexzoshxcfejaf", "claude_api_key": "YOUR API KEY", "text_to_image": "dall-e-2", "voice_to_text": "openai", "text_to_voice": "openai", "proxy": "", "hot_reload": false, "single_chat_prefix": ["客服", "@客服", "cursor", "augment", "购买", "教程"], "single_chat_reply_prefix": "", "group_chat_prefix": ["@客服", "cursor", "augment"], "group_name_white_list": ["Cursor售后群", "Augment售后群", "AI客服测试群"], "image_create_prefix": ["画"], "speech_recognition": true, "group_speech_recognition": false, "voice_reply_voice": false, "conversation_max_tokens": 2500, "expires_in_seconds": 3600, "character_desc": "你是专业的AI产品客服助手，主要负责Cursor登录助手和Augment续杯插件的销售与技术支持。你的沟通风格轻松、活泼、友好，就像和好朋友聊天一样自然。可以适当使用年轻化表达：'亲~'、'搞定！'、'小case~'、'没问题哒~'、'妥妥的~'等。当用户提问时，先快速给出核心答案，然后引导用户查看详细教程文档。回复要精准有效，直击痛点，适配微信聊天格式（不使用markdown符号）。", "temperature": 0.7, "subscribe_msg": "亲~ 欢迎来到AI产品客服！\n我是您的专属小助手，主要为您提供：\n🔹 Cursor登录助手 - 技术支持与购买咨询\n🔹 Augment续杯插件 - 使用教程与售后服务\n\n直接发送关键词即可获取帮助：\n• 发送'cursor'或'教程' - 获取Cursor相关信息\n• 发送'augment'或'续杯' - 获取Augment相关信息\n• 发送'购买'或'淘宝' - 获取购买链接\n\n有任何问题都可以直接问我哦~ 妥妥给您解决！", "use_linkai": false, "linkai_api_key": "", "linkai_app_code": "", "plugins": [{"plugin": "keyword", "enabled": true}]}