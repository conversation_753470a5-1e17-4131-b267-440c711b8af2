# 配置微信AI客服机器人 - 完整实施方案

## 任务目标
将 chatgpt-on-wechat 项目配置为专业的AI客服机器人，用于销售和支持 Cursor 和 Augment 登录助手产品。

## 具体实施步骤

### 1. 项目基础配置
**目标**: 使用 wcferry 方式接入微信，配置 SiliconFlow 的 DeepSeek-V3 模型

**具体要求**:
- 安装 wcferry 依赖包
- 创建 config.json 配置文件，使用以下API配置：
  - API接口: https://api.siliconflow.cn/v1
  - API密钥: sk-ptebmgpiesctnbdmeewgpboobiruklatpuaexzoshxcfejaf
  - 模型: Pro/deepseek-ai/DeepSeek-V3
- 配置微信触发前缀和群聊设置
- 设置 channel_type 为 "wcf"

### 2. AI客服人格配置
**目标**: 将AI配置为专业的产品客服助手

**具体要求**:
- 修改 config.json 中的 character_desc 字段
- 融合现有的 "Cursor登录小助手" 人格特征：
  - 使用轻松、活泼、友好的沟通方式
  - 使用年轻化表达："亲~", "搞定！", "小case~", "没问题哒~", "妥妥的~"
  - 先给核心答案，再引导查看详细文档
  - 回复格式适配微信（无markdown符号）
- 扩展为同时支持 Cursor 和 Augment 两个产品的客服

### 3. 知识库实现方案
**目标**: 基于 keyword 插件实现产品知识库，零代码修改

**具体要求**:
- 启用并配置 keyword 插件
- 创建关键词映射规则，包含：
  - Cursor相关关键词 → Cursor教程内容
  - Augment相关关键词 → Augment教程内容
  - 购买相关关键词 → 淘宝链接
  - 常见问题关键词 → 对应解答

**必须包含的链接资源**:
- Augment教程: https://w1yklj2r7gv.feishu.cn/wiki/WiYNwLCnti7kXmkDj5Zcl3hGnMb
- Cursor教程: https://w1yklj2r7gv.feishu.cn/wiki/RWz3wokbdihMsXktCyBcx7oCnsg
- Augment购买: https://item.taobao.com/item.htm?ft=t&id=953114980777
- Cursor购买: https://item.taobao.com/item.htm?id=931668414172

### 4. 参考文档分析
**要求**: 分析以下现有配置文件，提取可复用的配置和提示词：
- `之前的项目文档/config-之前的配置，包含提示词.yaml`
- `之前的项目文档/1. augment （号池）登陆助手教程.md`
- `之前的项目文档/2. cursor登陆助手的完整 售后文档-7.29.md`

### 5. 新手操作文档
**目标**: 创建完整的项目启动和维护指南

**要求**:
- 编写详细的项目启动步骤
- 说明如何修改和增补知识库内容
- 提供常见问题排查方法
- 更新项目 README.md 文件

### 6. 实施优先级
1. **最高优先级**: 基础配置 + AI人格设置（确保基本功能可用）
2. **高优先级**: keyword插件知识库配置（核心业务功能）
3. **中优先级**: 新手文档编写（后续维护支持）

## 预期结果
配置完成后，微信机器人应能够：
- 自动识别用户询问的产品类型（Cursor/Augment）
- 提供对应的教程链接和购买链接
- 以友好的客服语调进行交互
- 准确回答常见技术问题