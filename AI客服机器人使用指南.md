# AI客服机器人使用指南

## 项目概述

这是一个基于 chatgpt-on-wechat 框架的专业AI客服机器人，专门为 Cursor登录助手 和 Augment续杯插件 提供销售与技术支持服务。

### 核心特性
- 🤖 **智能客服**：基于 DeepSeek-V3 模型的专业AI客服
- 📱 **微信接入**：通过 wcferry 实现微信消息处理
- 🔍 **关键词匹配**：自动识别用户需求并提供精准回复
- 📚 **知识库**：内置完整的产品教程和常见问题解答
- 🛒 **销售支持**：自动提供购买链接和价格信息

## 快速启动

### 1. 环境准备

**系统要求**：
- Python 3.8+
- Windows 10/11 或 macOS
- 微信PC版（用于wcferry连接）

**安装依赖**：
```bash
pip install -r requirements.txt
```

### 2. 配置文件

项目已预配置完成，主要配置在 `config.json`：

```json
{
  "channel_type": "wcf",
  "model": "Pro/deepseek-ai/DeepSeek-V3",
  "open_ai_api_base": "https://api.siliconflow.cn/v1",
  "open_ai_api_key": "sk-ptebmgpiesctnbdmeewgpboobiruklatpuaexzoshxcfejaf"
}
```

### 3. 启动机器人

```bash
python app.py
```

启动后会自动：
1. 连接微信（需要微信PC版登录）
2. 加载AI模型和插件
3. 开始监听微信消息

## 功能说明

### 智能关键词识别

机器人会自动识别以下关键词并提供对应服务：

| 关键词 | 功能 |
|--------|------|
| `cursor` | Cursor登录助手相关信息 |
| `augment` | Augment续杯插件相关信息 |
| `购买`、`淘宝` | 购买链接和价格信息 |
| `教程` | 使用教程文档链接 |
| `激活` | 激活相关问题解答 |
| `limit` | 解决limit问题 |
| `锁机器` | 解决锁机器码问题 |
| `help` | 显示帮助信息 |

### 触发方式

**私聊触发**：
- 直接发送关键词
- 使用前缀：`客服`、`@客服`、`cursor`、`augment`、`购买`、`教程`

**群聊触发**：
- `@客服 [问题]`
- `cursor [问题]`
- `augment [问题]`

## 维护指南

### 1. 更新知识库

编辑 `plugins/keyword/config.json` 文件：

```json
{
  "keyword": {
    "新关键词": "对应的回复内容"
  }
}
```

**重启机器人**使配置生效。

### 2. 修改AI人格

编辑 `config.json` 中的 `character_desc` 字段：

```json
{
  "character_desc": "你是专业的AI产品客服助手..."
}
```

### 3. 添加新产品

1. 在 `plugins/keyword/config.json` 添加新产品关键词
2. 更新 `config.json` 中的触发前缀
3. 修改欢迎消息 `subscribe_msg`

### 4. 群聊管理

在 `config.json` 中配置允许的群聊：

```json
{
  "group_name_white_list": [
    "Cursor售后群",
    "Augment售后群",
    "新群名称"
  ]
}
```

## 常见问题

### Q: 机器人无法连接微信？
A: 确保微信PC版已登录，并且wcferry依赖已正确安装。

### Q: 关键词不生效？
A: 检查 `plugins/keyword/config.json` 格式是否正确，修改后需重启机器人。

### Q: AI回复不准确？
A: 可以调整 `config.json` 中的 `character_desc` 和 `temperature` 参数。

### Q: 如何查看运行日志？
A: 机器人运行时会在控制台输出日志信息，可以查看详细的处理过程。

## 技术支持

如需技术支持或功能定制，请联系：
- 微信群：AI客服技术交流群
- 邮箱：<EMAIL>

## 更新日志

### v1.0.0 (2024-07-29)
- ✅ 完成基础配置和部署
- ✅ 集成 wcferry 微信接入
- ✅ 配置 DeepSeek-V3 AI模型
- ✅ 实现关键词自动回复系统
- ✅ 内置 Cursor 和 Augment 产品知识库
- ✅ 支持私聊和群聊多种触发方式

---

**注意**：本机器人仅用于产品客服支持，请勿用于其他商业用途。
