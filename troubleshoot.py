#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI客服机器人故障排除工具
用于诊断和解决常见问题
"""

import os
import json
import sys
import subprocess

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，建议使用Python 3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_config_format():
    """检查配置文件格式"""
    print("\n📋 配置文件格式检查")
    
    # 检查主配置文件
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ config.json 格式正确")
        
        # 检查关键配置项
        required_keys = ['channel_type', 'model', 'open_ai_api_key', 'open_ai_api_base']
        for key in required_keys:
            if key in config:
                print(f"✅ {key}: {config[key]}")
            else:
                print(f"❌ 缺少配置项: {key}")
                
    except FileNotFoundError:
        print("❌ config.json 文件不存在")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ config.json 格式错误: {e}")
        return False
    
    # 检查keyword插件配置
    try:
        with open('plugins/keyword/config.json', 'r', encoding='utf-8') as f:
            keyword_config = json.load(f)
        print("✅ keyword插件配置格式正确")
        
        if 'keyword' in keyword_config:
            keyword_count = len(keyword_config['keyword'])
            print(f"✅ 已配置 {keyword_count} 个关键词")
        else:
            print("❌ keyword插件配置缺少'keyword'字段")
            
    except FileNotFoundError:
        print("❌ plugins/keyword/config.json 文件不存在")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ keyword插件配置格式错误: {e}")
        return False
    
    return True

def test_api_connection():
    """测试API连接"""
    print("\n🌐 API连接测试")
    
    try:
        import requests
        
        # 测试SiliconFlow API
        api_base = "https://api.siliconflow.cn/v1"
        test_url = f"{api_base}/models"
        
        print(f"测试连接: {api_base}")
        
        response = requests.get(test_url, timeout=10)
        if response.status_code == 200:
            print("✅ SiliconFlow API连接正常")
            return True
        else:
            print(f"❌ API连接失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络连接错误: {e}")
        return False
    except ImportError:
        print("❌ requests库未安装")
        return False

def check_wcferry():
    """检查wcferry状态"""
    print("\n📱 wcferry状态检查")
    
    try:
        import wcferry
        print("✅ wcferry库已安装")
        
        # 尝试创建wcferry实例（不实际连接）
        print("📡 检查微信连接能力...")
        print("⚠️  注意：需要微信PC版登录才能正常工作")
        return True
        
    except ImportError:
        print("❌ wcferry库未安装")
        print("请运行: pip install wcferry>=*********")
        return False
    except Exception as e:
        print(f"⚠️  wcferry检查异常: {e}")
        return True

def check_plugins():
    """检查插件状态"""
    print("\n🔌 插件状态检查")
    
    # 检查keyword插件
    if os.path.exists('plugins/keyword'):
        print("✅ keyword插件目录存在")
        
        if os.path.exists('plugins/keyword/__init__.py'):
            print("✅ keyword插件初始化文件存在")
        else:
            print("❌ keyword插件初始化文件缺失")
            
        if os.path.exists('plugins/keyword/keyword.py'):
            print("✅ keyword插件主文件存在")
        else:
            print("❌ keyword插件主文件缺失")
            
        if os.path.exists('plugins/keyword/config.json'):
            print("✅ keyword插件配置文件存在")
        else:
            print("❌ keyword插件配置文件缺失")
            
    else:
        print("❌ keyword插件目录不存在")
        return False
    
    return True

def generate_fix_suggestions():
    """生成修复建议"""
    print("\n🔧 常见问题修复建议")
    print("=" * 50)
    
    suggestions = [
        {
            "问题": "机器人无法启动",
            "解决方案": [
                "1. 检查Python版本是否为3.8+",
                "2. 安装所有依赖: pip install -r requirements.txt",
                "3. 检查config.json配置文件格式",
                "4. 确保微信PC版已登录"
            ]
        },
        {
            "问题": "关键词不生效",
            "解决方案": [
                "1. 检查plugins/keyword/config.json格式",
                "2. 确保keyword插件已在config.json中启用",
                "3. 重启机器人使配置生效",
                "4. 检查关键词是否包含特殊字符"
            ]
        },
        {
            "问题": "微信连接失败",
            "解决方案": [
                "1. 确保微信PC版已登录并保持在线",
                "2. 检查wcferry版本: pip install wcferry>=*********",
                "3. 重启微信PC版后再试",
                "4. 检查防火墙是否阻止连接"
            ]
        },
        {
            "问题": "AI回复异常",
            "解决方案": [
                "1. 检查API密钥是否正确",
                "2. 测试网络连接到api.siliconflow.cn",
                "3. 检查模型名称是否正确",
                "4. 查看控制台错误日志"
            ]
        }
    ]
    
    for i, item in enumerate(suggestions, 1):
        print(f"\n{i}. {item['问题']}")
        for solution in item['解决方案']:
            print(f"   {solution}")

def main():
    """主函数"""
    print("🔍 AI客服机器人故障排除工具")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 执行各项检查
    checks = [
        check_python_version,
        check_config_format,
        test_api_connection,
        check_wcferry,
        check_plugins
    ]
    
    for check in checks:
        try:
            if not check():
                all_checks_passed = False
        except Exception as e:
            print(f"❌ 检查过程出错: {e}")
            all_checks_passed = False
        print("-" * 30)
    
    # 总结
    print("\n📊 检查结果总结")
    if all_checks_passed:
        print("✅ 所有检查通过，系统状态良好！")
        print("如果仍有问题，请查看运行日志获取更多信息。")
    else:
        print("❌ 发现一些问题，请参考以下修复建议：")
        generate_fix_suggestions()
    
    print("\n" + "=" * 50)
    print("如需更多帮助，请查看：AI客服机器人使用指南.md")

if __name__ == "__main__":
    main()
