#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI客服机器人启动脚本
专为 Cursor登录助手 和 Augment续杯插件 提供客服支持

使用方法：
python start_ai_customer_service.py
"""

import os
import sys
import time
import subprocess

def check_dependencies():
    """检查必要的依赖是否已安装"""
    print("🔍 检查依赖环境...")
    
    required_packages = [
        'openai',
        'requests', 
        'wcferry',
        'web.py'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} - 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包：{', '.join(missing_packages)}")
        print("请运行以下命令安装：")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖检查通过！")
    return True

def check_config():
    """检查配置文件是否存在"""
    print("\n📋 检查配置文件...")
    
    if not os.path.exists('config.json'):
        print("❌ config.json 配置文件不存在")
        print("请确保配置文件已正确创建")
        return False
    
    if not os.path.exists('plugins/keyword/config.json'):
        print("❌ keyword插件配置文件不存在")
        print("请确保 plugins/keyword/config.json 文件已创建")
        return False
    
    print("✅ 配置文件检查通过！")
    return True

def check_wechat():
    """检查微信PC版是否运行"""
    print("\n💬 检查微信状态...")
    
    # 在Windows上检查微信进程
    if sys.platform == "win32":
        try:
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq WeChat.exe'], 
                                  capture_output=True, text=True)
            if 'WeChat.exe' in result.stdout:
                print("✅ 微信PC版正在运行")
                return True
            else:
                print("⚠️  未检测到微信PC版进程")
                print("请确保微信PC版已登录并运行")
                return False
        except:
            print("⚠️  无法检测微信状态，请手动确认微信PC版已登录")
            return True
    else:
        print("⚠️  请手动确认微信PC版已登录并运行")
        return True

def start_bot():
    """启动AI客服机器人"""
    print("\n🚀 启动AI客服机器人...")
    print("=" * 50)
    print("🤖 Cursor & Augment AI客服助手")
    print("📱 微信接入模式：wcferry")
    print("🧠 AI模型：DeepSeek-V3")
    print("🔧 功能：智能客服 + 关键词回复")
    print("=" * 50)
    print("\n⏳ 正在启动，请稍候...")
    
    try:
        # 导入并运行主程序
        from app import run
        run()
    except KeyboardInterrupt:
        print("\n\n👋 AI客服机器人已停止运行")
        print("感谢使用！")
    except Exception as e:
        print(f"\n❌ 启动失败：{e}")
        print("\n🔧 故障排除建议：")
        print("1. 检查微信PC版是否正常登录")
        print("2. 检查网络连接是否正常")
        print("3. 检查配置文件是否正确")
        print("4. 查看详细错误日志")

def main():
    """主函数"""
    print("🎯 AI客服机器人启动检查")
    print("专为 Cursor登录助手 & Augment续杯插件 提供客服支持\n")
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    # 检查配置
    if not check_config():
        input("\n按回车键退出...")
        return
    
    # 检查微信
    wechat_ok = check_wechat()
    if not wechat_ok:
        choice = input("\n是否继续启动？(y/n): ").lower()
        if choice != 'y':
            return
    
    # 启动机器人
    start_bot()

if __name__ == "__main__":
    main()
