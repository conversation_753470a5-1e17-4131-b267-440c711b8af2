#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化启动脚本 - 避免编码问题
"""

import os
import sys
import subprocess

def main():
    """主函数"""
    print("AI Customer Service Bot - Simple Starter")
    print("=" * 50)
    
    # 设置环境变量避免编码问题
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    
    try:
        print("Starting bot...")
        
        # 启动机器人
        process = subprocess.Popen(
            [sys.executable, 'app.py'],
            env=env,
            cwd=os.getcwd()
        )
        
        print("Bot started successfully!")
        print("Press Ctrl+C to stop the bot")
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\nStopping bot...")
        if 'process' in locals():
            process.terminate()
        print("Bot stopped.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
