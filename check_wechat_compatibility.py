#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
微信兼容性检查工具
用于检查微信版本和wcferry兼容性
"""

import subprocess
import sys
import time
import psutil

def get_wechat_version():
    """获取微信版本信息"""
    try:
        # 查找微信进程
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            if proc.info['name'] and 'WeChat.exe' in proc.info['name']:
                exe_path = proc.info['exe']
                if exe_path:
                    # 获取文件版本信息
                    import win32api
                    info = win32api.GetFileVersionInfo(exe_path, "\\")
                    ms = info['FileVersionMS']
                    ls = info['FileVersionLS']
                    version = f"{win32api.HIWORD(ms)}.{win32api.LOWORD(ms)}.{win32api.HIWORD(ls)}.{win32api.LOWORD(ls)}"
                    return version, exe_path
    except Exception as e:
        print(f"获取微信版本失败: {e}")
    return None, None

def check_wcferry_version():
    """检查wcferry版本"""
    try:
        import wcferry
        return wcferry.__version__
    except ImportError:
        return None
    except AttributeError:
        return "未知版本"

def check_wechat_process():
    """检查微信进程状态"""
    wechat_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'status', 'memory_info']):
        if proc.info['name'] and 'WeChat' in proc.info['name']:
            wechat_processes.append({
                'pid': proc.info['pid'],
                'name': proc.info['name'],
                'status': proc.info['status'],
                'memory_mb': round(proc.info['memory_info'].rss / 1024 / 1024, 2)
            })
    return wechat_processes

def test_wcferry_connection():
    """测试wcferry连接"""
    try:
        from wcferry import Wcf
        wcf = Wcf()
        
        # 尝试连接
        print("正在测试wcferry连接...")
        time.sleep(2)
        
        # 检查登录状态
        is_login = wcf.is_login()
        print(f"微信登录状态: {'已登录' if is_login else '未登录'}")
        
        if is_login:
            # 获取用户信息
            user_info = wcf.get_user_info()
            print(f"当前用户: {user_info.get('name', '未知')}")
        
        wcf.cleanup()
        return True
        
    except Exception as e:
        print(f"wcferry连接测试失败: {e}")
        return False

def get_compatible_versions():
    """获取兼容版本列表"""
    return {
        "推荐版本": [
            "3.9.11.25",
            "3.9.10.19", 
            "3.9.9.43"
        ],
        "测试版本": [
            "3.9.8.25",
            "3.9.7.29",
            "3.9.2.23"
        ]
    }

def provide_solutions(wechat_version, wcferry_version):
    """提供解决方案"""
    print("\n🔧 解决方案建议:")
    print("=" * 50)
    
    compatible_versions = get_compatible_versions()
    
    if wechat_version:
        is_compatible = False
        for category, versions in compatible_versions.items():
            if wechat_version in versions:
                is_compatible = True
                print(f"✅ 当前微信版本 {wechat_version} 在{category}列表中")
                break
        
        if not is_compatible:
            print(f"⚠️  当前微信版本 {wechat_version} 可能不兼容")
            print("\n推荐操作:")
            print("1. 下载兼容的微信版本:")
            for category, versions in compatible_versions.items():
                print(f"   {category}: {', '.join(versions)}")
    
    if wcferry_version:
        print(f"\n📦 当前wcferry版本: {wcferry_version}")
        if wcferry_version != "********":
            print("建议更新到最新稳定版本:")
            print("   pip uninstall wcferry")
            print("   pip install wcferry==********")
    
    print("\n🛠️  通用解决方案:")
    print("1. 重启微信PC版")
    print("2. 以管理员身份运行Python脚本")
    print("3. 关闭杀毒软件的实时保护")
    print("4. 确保微信PC版不是最新版本")
    print("5. 尝试使用微信多开工具")
    
    print("\n⚠️  注意事项:")
    print("• 不要使用微信最新版本")
    print("• 避免微信自动更新")
    print("• 确保网络连接稳定")
    print("• 定期重启微信和机器人")

def main():
    """主函数"""
    print("🔍 微信兼容性检查工具")
    print("=" * 50)
    
    # 检查微信版本
    print("\n1. 检查微信版本...")
    wechat_version, wechat_path = get_wechat_version()
    if wechat_version:
        print(f"✅ 微信版本: {wechat_version}")
        print(f"✅ 微信路径: {wechat_path}")
    else:
        print("❌ 未检测到微信进程或无法获取版本信息")
    
    # 检查wcferry版本
    print("\n2. 检查wcferry版本...")
    wcferry_version = check_wcferry_version()
    if wcferry_version:
        print(f"✅ wcferry版本: {wcferry_version}")
    else:
        print("❌ wcferry未安装")
    
    # 检查微信进程
    print("\n3. 检查微信进程状态...")
    processes = check_wechat_process()
    if processes:
        for proc in processes:
            print(f"✅ 进程: {proc['name']} (PID: {proc['pid']}, 状态: {proc['status']}, 内存: {proc['memory_mb']}MB)")
    else:
        print("❌ 未检测到微信进程")
    
    # 测试wcferry连接
    if wcferry_version and processes:
        print("\n4. 测试wcferry连接...")
        connection_ok = test_wcferry_connection()
        if connection_ok:
            print("✅ wcferry连接测试通过")
        else:
            print("❌ wcferry连接测试失败")
    
    # 提供解决方案
    provide_solutions(wechat_version, wcferry_version)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n检查已取消")
    except Exception as e:
        print(f"\n检查过程出错: {e}")
        print("请确保以管理员身份运行此脚本")
